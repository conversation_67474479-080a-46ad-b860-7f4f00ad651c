<template>
  <div class="userInfo">
    <a-card
      :bordered="false"
      class="userInfo-container"
    >
      <div class="user_avatar">
        <div class="avatar-container" @click="isEditing && handleAvatarClick()">
          <img
            :src="userInfo.avatar || defaultAvatar"
            referrerpolicy="no-referrer"
            alt="用户头像"
            @error="handleImageError"
          />
          <div v-if="isEditing" class="avatar-overlay">
            <div class="upload-icon">📷</div>
            <div class="upload-text">点击上传</div>
          </div>
        </div>
        <input
          ref="avatarInput"
          type="file"
          accept="image/*"
          style="display: none"
          @change="handleAvatarUpload"
        />
        <div
          v-if="userInfo.subscription && (userInfo.subscription.toLowerCase() === 'pro' || userInfo.subscription.toLowerCase() === 'ultra')"
          class="vip-badge"
        >
          VIP/{{ userInfo.subscription }}
        </div>
      </div>
      <div class="registration_type">
        {{
          userInfo.subscription && (userInfo.subscription.toLowerCase() === 'pro' || userInfo.subscription.toLowerCase() === 'ultra')
            ? t('userInfo.vip')
            : userInfo.registrationType === 1
              ? t('userInfo.enterprise')
              : t('userInfo.personal')
        }}
        <a-tag
          v-if="userInfo.expires && new Date() < new Date(userInfo.expires)"
          :key="userInfo.subscription"
          :title="t('userInfo.expirationTime') + `：${userInfo.expires}`"
          class="subscription-tag"
        >
          {{ userInfo.subscription ? userInfo.subscription.charAt(0).toUpperCase() + userInfo.subscription.slice(1) : '-' }}
        </a-tag>
      </div>
      <div class="divider-container">
        <a-divider style="border-color: var(--border-color-light, #e8e8e8); margin-bottom: 20px" />
      </div>

      <a-form
        :label-col="{ span: 10, offset: 2 }"
        :wrapper-col="{ span: 12 }"
        class="custom-form"
        :model="formState"
      >
        <a-form-item
          label="UID"
          class="user_my-ant-form-item"
        >
          {{ userInfo.uid }}
        </a-form-item>
        <a-form-item
          :label="t('userInfo.name')"
          class="user_my-ant-form-item"
          name="name"
        >
          <a-input
            v-if="isEditing"
            v-model:value="formState.name"
            :placeholder="t('userInfo.pleaseInputName') + '（选填）'"
            class="custom-input"
          />
          <span v-else>{{ userInfo.name || '未设置' }}</span>
        </a-form-item>
        <a-form-item
          :label="t('userInfo.username')"
          class="user_my-ant-form-item"
          name="username"
        >
          <a-input
            v-if="isEditing"
            v-model:value="formState.username"
            :placeholder="t('userInfo.pleaseInputUsername') + '（选填）'"
            class="custom-input"
          />
          <span v-else>{{ userInfo.username || '未设置' }}</span>
        </a-form-item>
        <a-form-item
          :label="t('userInfo.mobile')"
          class="user_my-ant-form-item"
          name="mobile"
        >
          <a-input
            v-if="isEditing"
            v-model:value="formState.mobile"
            :placeholder="t('userInfo.pleaseInputMobile') + '（选填）'"
            class="custom-input"
          />
          <span v-else>{{ userInfo.mobile || '未设置' }}</span>
        </a-form-item>
        <a-form-item
          :label="t('userInfo.password')"
          class="user_my-ant-form-item"
        >
          <template v-if="isEditingPassword">
            <a-input-password
              v-model:value="formState.newPassword"
              :placeholder="t('userInfo.pleaseInputNewPassword')"
              class="custom-input"
            />
            <a-button
              type="link"
              style="width: 40px"
              :icon="h(CheckOutlined)"
              @click="handleSave"
            />
            <a-button
              type="link"
              style="width: 40px; color: #ff4d4f"
              :icon="h(CloseOutlined)"
              @click="cancelEditing"
            ></a-button>
          </template>

          <template v-else>
            <span>****************</span>
            <a-button
              v-if="!unChange && !isEditing"
              type="link"
              style="margin-left: 8px"
              @click="startEditingPassword"
            >
              {{ t('userInfo.resetPassword') }}
            </a-button>
          </template>
        </a-form-item>
        <a-form-item
          v-if="isEditingPassword"
          :label="t('userInfo.passwordStrength')"
        >
          <span
            v-if="strength == 1"
            style="color: red"
            >{{ t('userInfo.passwordStrengthWeak') }}</span
          >
          <span
            v-if="strength == 2"
            style="color: #d46b08"
            >{{ t('userInfo.passwordStrengthMedium') }}</span
          >
          <span
            v-if="strength > 2"
            style="color: rgb(50, 100, 237)"
            >{{ t('userInfo.passwordStrengthStrong') }}</span
          >
        </a-form-item>
        <a-form-item
          :label="t('userInfo.email')"
          class="user_my-ant-form-item"
        >
          <template v-if="isEditing">
            <a-input
              v-model:value="formState.email"
              :placeholder="t('userInfo.emailPlaceholder') + '（选填）'"
              class="custom-input"
            />
          </template>
          <template v-else>
            {{ userInfo.email || '未设置' }}
          </template>
        </a-form-item>
        <a-form-item
          v-if="userInfo.secondaryOrganization"
          :label="t('userInfo.organization')"
          class="user_my-ant-form-item"
        >
          {{ userInfo.secondaryOrganization }}/{{ userInfo.tertiaryOrganization }}/{{ userInfo.team }}
        </a-form-item>
        <a-form-item
          :label="t('userInfo.ip')"
          class="user_my-ant-form-item"
        >
          {{ userInfo.localIp }}
        </a-form-item>
        <a-form-item
          :label="t('userInfo.macAddress')"
          class="user_my-ant-form-item"
        >
          {{ userInfo.macAddress }}
          <img
            v-if="userInfo.isOfficeDevice"
            :src="enterpriseCertificationIcon"
            :alt="t('userInfo.enterpriseCertification')"
            class="enterprise-certification-icon"
            :title="t('userInfo.enterpriseCertification')"
          />
        </a-form-item>
      </a-form>
      <div
        v-if="!unChange && !isEditingPassword"
        class="button-container"
      >
        <template v-if="!isEditing">
          <a-button
            type="primary"
            @click="startEditing"
          >
            {{ t('userInfo.edit') }}
          </a-button>
        </template>
        <template v-else>
          <a-button
            type="primary"
            style="margin-right: 8px"
            @click="handleSave"
          >
            {{ t('userInfo.save') }}
          </a-button>
          <a-button @click="cancelEditing">
            {{ t('userInfo.cancel') }}
          </a-button>
        </template>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, reactive, computed, h } from 'vue'
import 'xterm/css/xterm.css'
import i18n from '@/locales'
import { updateUser, changePassword } from '@api/user/user'
import { CloseOutlined, CheckOutlined } from '@ant-design/icons-vue'
import { useDeviceStore } from '@/store/useDeviceStore'
import { userInfoStore } from '@/store/index'
import { message } from 'ant-design-vue'
import zxcvbn from 'zxcvbn'
import enterpriseCertificationIcon from '@/assets/icons/enterprise-certification.svg'
import defaultAvatarUrl from '@/assets/logo.svg'

const { t } = i18n.global
const deviceStore = useDeviceStore()
const store = userInfoStore()
const userInfo = ref({})
const isEditing = ref(false)
const isEditingPassword = ref(false)
const unChange = ref(true)
const avatarInput = ref(null)
const defaultAvatar = defaultAvatarUrl

const formState = reactive({
  username: '',
  name: '',
  mobile: '',
  email: '',
  newPassword: ''
})

const getUserInfo = async () => {
  // 首先确保设备信息已初始化
  deviceStore.initDeviceInfo()

  // 直接使用本地模式，避免API请求失败
  console.log('使用本地模式显示用户信息')
  setDefaultUserInfo()

  // 注释掉API请求以避免错误日志
  /*
  try {
    // 尝试从API获取用户信息
    const res = await getUser()
    if (res && res.data) {
      userInfo.value = res.data
      userInfo.value.localIp = deviceStore.getDeviceIp || '127.0.0.1'
      userInfo.value.macAddress = deviceStore.getMacAddress || 'Unknown'

      // 检查设备信息
      try {
        const deviceRes = await checkUserDevice({
          ip: userInfo.value.localIp,
          macAddress: userInfo.value.macAddress
        })
        if (deviceRes && deviceRes.code === 200) {
          userInfo.value.isOfficeDevice = deviceRes.data.isOfficeDevice
        }
      } catch (error) {
        console.warn('Failed to check device info:', error)
        userInfo.value.isOfficeDevice = false
      }

      if (userInfo.value.uid !== 2000001) unChange.value = false

      // 初始化表单数据
      formState.username = userInfo.value.username || ''
      formState.name = userInfo.value.name || ''
      formState.mobile = userInfo.value.mobile || ''
      formState.email = userInfo.value.email || ''
    } else {
      // API返回但没有数据时的处理
      setDefaultUserInfo()
    }
  } catch (error) {
    console.warn('API请求失败，使用本地模式:', error)
    // API调用失败时使用本地模式
    setDefaultUserInfo()
  }
  */
}

/**
 * 设置默认用户信息（本地模式）
 */
const setDefaultUserInfo = () => {
  // 确保设备信息已初始化
  deviceStore.initDeviceInfo()

  // 生成一个基于设备的唯一ID
  const deviceId = generateDeviceBasedId()

  // 尝试从本地存储恢复用户信息
  let savedUserInfo = null
  try {
    const saved = localStorage.getItem('local_user_info')
    if (saved) {
      savedUserInfo = JSON.parse(saved)
    }
  } catch (error) {
    console.warn('无法读取本地用户信息:', error)
  }

  // 获取设备信息
  const deviceIp = deviceStore.getDeviceIp || '127.0.0.1'
  const deviceMac = deviceStore.getMacAddress || '00:00:52:db:d2:a4'

  console.log('设备信息:', { deviceIp, deviceMac, deviceId })

  userInfo.value = {
    uid: deviceId,
    username: savedUserInfo?.username || 'local_user',
    name: savedUserInfo?.name || '本地用户',
    email: savedUserInfo?.email || '<EMAIL>',
    mobile: savedUserInfo?.mobile || '',
    avatar: savedUserInfo?.avatar || defaultAvatarUrl,
    subscription: 'free',
    registrationType: 0,
    localIp: deviceIp,
    macAddress: deviceMac,
    isOfficeDevice: false
  }

  // 允许编辑本地用户信息
  unChange.value = false

  // 初始化表单数据
  formState.username = userInfo.value.username
  formState.name = userInfo.value.name
  formState.mobile = userInfo.value.mobile
  formState.email = userInfo.value.email

  // 同步更新全局userInfoStore，确保水印显示正确的用户信息
  store.updateInfo({
    name: userInfo.value.name,
    email: userInfo.value.email,
    avatar: userInfo.value.avatar,
    registrationType: userInfo.value.registrationType,
    token: '',
    uid: userInfo.value.uid
  })

  console.log('用户信息已设置:', userInfo.value)
  console.log('已同步到全局store:', store.userInfo)
}

/**
 * 基于设备信息生成唯一ID
 */
const generateDeviceBasedId = () => {
  // 确保设备信息已初始化
  deviceStore.initDeviceInfo()

  const mac = deviceStore.getMacAddress || 'unknown'
  const ip = deviceStore.getDeviceIp || 'unknown'
  const userAgent = navigator.userAgent || 'unknown'
  const screen = `${window.screen.width}x${window.screen.height}`

  // 简单的哈希算法生成设备ID
  let hash = 0
  const str = mac + ip + userAgent + screen
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = (hash << 5) - hash + char
    hash = hash & hash // 转换为32位整数
  }

  // 确保生成的ID是正数且在合理范围内
  const deviceId = (Math.abs(hash) % 2000000000) + 1000000000
  console.log('生成设备ID:', deviceId, '基于:', { mac, ip, userAgent: userAgent.substring(0, 50) })

  return deviceId
}
// const strength = zxcvbn(password)
const strength = computed(() => {
  if (formState.newPassword === '') return null
  else return zxcvbn(formState.newPassword).score
})

const startEditing = () => {
  isEditing.value = true
  isEditingPassword.value = false
}

const startEditingPassword = () => {
  isEditingPassword.value = true
  isEditing.value = false
}

const cancelEditing = () => {
  isEditing.value = false
  isEditingPassword.value = false
  // 重置表单数据
  formState.name = userInfo.value.name
  formState.mobile = userInfo.value.mobile
  formState.email = userInfo.value.email
  formState.newPassword = ''
}

const validatePassword = () => {
  if (formState.newPassword.length < 6) {
    message.error(t('userInfo.passwordLengthError'))
    return false
  }
  if (strength.value < 1) {
    message.error(t('userInfo.passwordStrengthError'))
    return false
  }
  return true
}
const validateSave = () => {
  // 手机号验证 - 选填，如果填写则需要格式正确
  if (formState.mobile && formState.mobile.trim() !== '' && !/^1[3-9]\d{9}$/.test(formState.mobile)) {
    message.error(t('userInfo.mobileInvalid'))
    return false
  }

  // 用户名验证 - 选填，如果填写则需要格式正确
  if (formState.username && formState.username.trim() !== '') {
    if (formState.username.length < 6 || formState.username.length > 20) {
      message.error(t('userInfo.usernameLengthError'))
      return false
    }
    if (!/^[a-zA-Z0-9_]+$/.test(formState.username)) {
      message.error(t('userInfo.usernameFormatError'))
      return false
    }
  }

  // 名称验证 - 选填，如果填写则需要长度合理
  if (formState.name && formState.name.trim() !== '' && formState.name.length > 20) {
    message.error(t('userInfo.nameTooLong'))
    return false
  }

  // 邮箱验证 - 选填，如果填写则需要格式正确
  if (formState.email && formState.email.trim() !== '') {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(formState.email)) {
      message.error('邮箱格式不正确')
      return false
    }
  }

  return true
}
const handleSave = async () => {
  try {
    if (isEditingPassword.value) {
      if (!validatePassword()) {
        return
      }

      try {
        const response = await changePassword({
          password: formState.newPassword
        })
        if (response.code == 200) {
          message.success(t('userInfo.passwordResetSuccess'))
          cancelEditing()
        } else {
          message.error(response.message || t('userInfo.passwordResetFailed'))
        }
      } catch (error) {
        console.warn('密码修改API不可用，本地模式下不支持密码修改')
        message.warning('本地模式下不支持密码修改功能')
        cancelEditing()
      }
    } else {
      if (!validateSave()) return

      try {
        const response = await updateUser({
          username: formState.username,
          name: formState.name,
          mobile: formState.mobile,
          email: formState.email
        })
        console.log(response)
        if (response.code == 200) {
          message.success(t('userInfo.updateSuccess'))
          cancelEditing()
          getUserInfo()
        } else {
          message.error(response.message || t('userInfo.updateFailed'))
        }
      } catch (error) {
        console.warn('用户信息更新API不可用，使用本地保存')
        // 本地模式下直接更新用户信息
        userInfo.value.username = formState.username
        userInfo.value.name = formState.name
        userInfo.value.mobile = formState.mobile
        userInfo.value.email = formState.email

        // 同步更新全局userInfoStore，确保水印显示最新的用户信息
        store.updateInfo({
          name: userInfo.value.name,
          email: userInfo.value.email,
          avatar: userInfo.value.avatar,
          registrationType: userInfo.value.registrationType,
          token: store.userInfo.token,
          uid: userInfo.value.uid
        })

        // 保存到本地存储
        try {
          localStorage.setItem(
            'local_user_info',
            JSON.stringify({
              username: formState.username,
              name: formState.name,
              mobile: formState.mobile,
              email: formState.email
            })
          )
          message.success('用户信息已保存到本地')
        } catch (storageError) {
          console.warn('本地存储失败:', storageError)
          message.success('用户信息已更新（会话期间有效）')
        }

        console.log('用户信息已更新并同步到全局store:', store.userInfo)
        cancelEditing()
      }
    }
  } catch (error) {
    console.error('保存失败:', error)
    message.error(isEditingPassword.value ? t('userInfo.passwordResetFailed') : t('userInfo.updateFailed'))
  }
}

/**
 * 处理头像点击事件
 */
const handleAvatarClick = () => {
  if (avatarInput.value) {
    avatarInput.value.click()
  }
}

/**
 * 处理头像上传
 */
const handleAvatarUpload = (event) => {
  const file = event.target.files[0]
  if (!file) return

  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    message.error('请选择图片文件')
    return
  }

  // 验证文件大小（限制为5MB）
  if (file.size > 5 * 1024 * 1024) {
    message.error('图片大小不能超过5MB')
    return
  }

  // 读取文件并转换为base64
  const reader = new FileReader()
  reader.onload = (e) => {
    const base64 = e.target.result
    
    // 更新头像
    userInfo.value.avatar = base64
    
    // 同步更新全局store
    store.updateInfo({
      ...store.userInfo,
      avatar: base64
    })

    // 保存到本地存储
    try {
      const savedUserInfo = JSON.parse(localStorage.getItem('local_user_info') || '{}')
      savedUserInfo.avatar = base64
      localStorage.setItem('local_user_info', JSON.stringify(savedUserInfo))
      message.success('头像上传成功')
    } catch (error) {
      console.warn('头像保存到本地存储失败:', error)
      message.success('头像已更新（会话期间有效）')
    }
  }
  
  reader.onerror = () => {
    message.error('图片读取失败，请重试')
  }
  
  reader.readAsDataURL(file)
  
  // 清空input值，允许重复选择同一文件
  event.target.value = ''
}

/**
 * 处理图片加载错误
 */
const handleImageError = () => {
  console.warn('头像加载失败，使用默认头像')
  userInfo.value.avatar = defaultAvatar
}

// 初始化终端
onMounted(() => {
  getUserInfo()
})

// 销毁时清理资源
onBeforeUnmount(() => {})
</script>

<style scoped>
.userInfo {
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-right: none;
}

.userInfo-container {
  width: 100%;
  height: 100%;
  background-color: var(--bg-color);
  border-radius: 6px;
  overflow: hidden;
  padding: 4px;
  box-shadow: none;
  color: var(--text-color);
  border: none;
}

:deep(.ant-card) {
  height: 100%;
  background-color: var(--bg-color);
  border: none;
  box-shadow: none;
}

:deep(.ant-card-body) {
  border: none;
}

:deep(.ant-card-body) {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow-y: auto;
  border: none;
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb-color, rgba(0, 0, 0, 0.2)) transparent;
}

/* 自定义滚动条样式 */
:deep(.ant-card-body)::-webkit-scrollbar {
  width: 6px;
}

:deep(.ant-card-body)::-webkit-scrollbar-track {
  background: transparent;
}

:deep(.ant-card-body)::-webkit-scrollbar-thumb {
  background-color: var(--scrollbar-thumb-color, rgba(0, 0, 0, 0.2));
  border-radius: 3px;
}

/* 表单样式 */
.custom-form {
  color: var(--text-color);
  align-content: center;
  width: 100%;
}

.custom-form :deep(.ant-form-item-label) {
  padding-right: 1px;
}

.custom-form :deep(.ant-form-item-label > label) {
  color: var(--text-color);
}

/* 输入框样式 */
.custom-input,
:deep(.ant-input-password),
:deep(.ant-input-password .ant-input) {
  background-color: var(--bg-color) !important;
  color: var(--text-color) !important;
  border-radius: 4px !important;
  width: 250px !important;
}

.custom-input::placeholder,
:deep(.ant-input-password .ant-input)::placeholder {
  color: var(--text-color-secondary);
}

.custom-input:hover,
.custom-input:focus,
:deep(.ant-input-password:hover),
:deep(.ant-input-password-focused) {
  border-color: #2a82e4 !important;
  box-shadow: 0 0 0 2px rgba(42, 130, 228, 0.2) !important;
}

:deep(.ant-input-password .anticon) {
  color: #f8f8f8 !important;
}

/* 错误提示样式 */
.custom-form :deep(.ant-form-item-explain-error) {
  color: #ff4d4f;
}

.custom-form :deep(.ant-form-item-required::before) {
  color: #ff4d4f;
}

/* 表单项样式 */
.user_my-ant-form-item {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: var(--text-color);
  font-size: 30px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  margin-bottom: 8px;
  vertical-align: top;
  width: 100%;
}

/* 密码强度表单项样式覆盖 */
.custom-form :deep(.ant-form-item) {
  margin-bottom: 8px;
}

/* 头像样式 */
.user_avatar {
  width: 18vmin;
  height: 18vmin;
  margin: 0 auto 20px;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
  flex-shrink: 0;
}

.avatar-container {
  position: relative;
  width: 100%;
  height: 100%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.avatar-container:hover .avatar-overlay {
  opacity: 1;
}

.user_avatar img {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 50%;
  color: white;
  z-index: 5;
}

.upload-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.upload-text {
  font-size: 12px;
  font-weight: 500;
}

.vip-badge {
  position: absolute;
  bottom: 2%;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #ffd700, #ff8f00);
  color: #000;
  font-size: 8px;
  font-weight: bold;
  text-transform: uppercase;
  padding: 1px 6px;
  border-radius: 8px;
  z-index: 10;
  letter-spacing: 0.8px;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}

/* 按钮容器样式 */
.button-container {
  margin-top: 20px;
  text-align: center;
}

.button-container .ant-btn {
  min-width: 80px;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .user_avatar {
    width: 20vmin;
    height: 20vmin;
  }
}

.registration_type {
  text-align: center;
  font-weight: bold;
}

.divider-container {
  width: 70%;
  margin: 0 auto;
  text-align: center;
}

.divider-container :deep(.ant-divider) {
  border-color: var(--border-color-light);
}

.subscription-tag {
  background-color: rgba(42, 130, 228, 0.15);
  color: #1890ff;
  border: 1px solid rgba(42, 130, 228, 0.3);
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  padding: 1px 6px;
  letter-spacing: 0.5px;
}

.free-tag {
  background-color: rgba(128, 128, 128, 0.1);
  border-color: rgba(128, 128, 128, 0.15);
  color: var(--text-color-secondary, #999);
}

.enterprise-certification-icon {
  width: 24px;
  height: 24px;
  vertical-align: middle;
}
</style>
