<template>
  <div class="room-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <button
          class="btn btn-back"
          title="返回企业资源管理"
          @click="goBack"
        >
          <ArrowLeft class="btn-icon" />
          <span class="btn-text">返回</span>
        </button>
        <div class="header-content">
          <h1 class="page-title">
            <Building class="title-icon" />
            机房管理中心
          </h1>
          <p class="page-description">统一管理数据中心机房资源、环境监控和设备状态</p>
        </div>
      </div>
      <div class="header-actions">
        <button
          class="btn btn-secondary"
          @click="refreshData"
        >
          <RefreshCw class="btn-icon" />
          刷新
        </button>
        <button
          class="btn btn-primary"
          @click="showAddRoomDialog = true"
        >
          <Plus class="btn-icon" />
          新增机房
        </button>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-section">
      <div class="stats-grid">
        <div class="stat-card success">
          <div class="card-header">
            <div class="card-icon">
              <CheckCircle class="icon" />
            </div>
            <div class="card-meta">
              <span class="trend-indicator positive">+5%</span>
              <span class="trend-text">较上月</span>
            </div>
          </div>
          <div class="card-content">
            <div class="metric-value">{{ normalRooms }}</div>
            <div class="metric-label">运行机房</div>
            <div class="metric-description">当前运行状态良好</div>
          </div>
        </div>

        <div class="stat-card warning">
          <div class="card-header">
            <div class="card-icon">
              <AlertTriangle class="icon" />
            </div>
            <div class="card-meta">
              <span class="trend-indicator negative">-2%</span>
              <span class="trend-text">较上月</span>
            </div>
          </div>
          <div class="card-content">
            <div class="metric-value">{{ warningRooms }}</div>
            <div class="metric-label">告警机房</div>
            <div class="metric-description">需要关注温湿度</div>
          </div>
        </div>

        <div class="stat-card danger">
          <div class="card-header">
            <div class="card-icon">
              <XCircle class="icon" />
            </div>
            <div class="card-meta">
              <span class="trend-indicator neutral">0%</span>
              <span class="trend-text">较上月</span>
            </div>
          </div>
          <div class="card-content">
            <div class="metric-value">{{ criticalRooms }}</div>
            <div class="metric-label">故障机房</div>
            <div class="metric-description">设备离线或异常</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="card-header">
            <div class="card-icon">
              <Activity class="icon" />
            </div>
            <div class="card-meta">
              <span class="trend-indicator positive">+12%</span>
              <span class="trend-text">较上月</span>
            </div>
          </div>
          <div class="card-content">
            <div class="metric-value">{{ totalRacks }}</div>
            <div class="metric-label">机柜总数</div>
            <div class="metric-description">设备承载能力</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar-section">
      <div class="toolbar-left">
        <div class="search-box">
          <Search class="search-icon" />
          <input
            v-model="searchKeyword"
            type="text"
            placeholder="搜索机房名称或位置..."
            class="search-input"
            @input="handleSearch"
          />
        </div>
        <div class="filter-tabs">
          <button
            class="filter-tab"
            :class="{ active: statusFilter === 'all' }"
            @click="statusFilter = 'all'"
          >
            全部 ({{ totalRooms }})
          </button>
          <button
            class="filter-tab"
            :class="{ active: statusFilter === 'normal' }"
            @click="statusFilter = 'normal'"
          >
            正常 ({{ normalRooms }})
          </button>
          <button
            class="filter-tab"
            :class="{ active: statusFilter === 'warning' }"
            @click="statusFilter = 'warning'"
          >
            告警 ({{ warningRooms }})
          </button>
          <button
            class="filter-tab"
            :class="{ active: statusFilter === 'critical' }"
            @click="statusFilter = 'critical'"
          >
            故障 ({{ criticalRooms }})
          </button>
        </div>
      </div>
      <div class="toolbar-right">
        <button
          class="btn btn-secondary"
          @click="exportData"
        >
          <Download class="btn-icon" />
          导出
        </button>
        <button
          class="btn btn-secondary"
          @click="refreshData"
        >
          <RefreshCw class="btn-icon" />
          刷新
        </button>
      </div>
    </div>

    <!-- 机房列表区域 -->
    <div class="rooms-section">
      <div class="rooms-grid">
        <div
          v-for="room in filteredRooms"
          :key="room.id"
          class="room-card"
          :class="room.status"
          @dblclick="selectRoom(room)"
          title="双击进入机柜布局管理"
        >
          <div class="room-header">
            <div class="room-status">
              <div
                class="status-indicator"
                :class="room.status"
              ></div>
              <span class="status-text">{{ getStatusText(room.status) }}</span>
            </div>
            <div class="room-actions">
              <button
                class="action-btn"
                @click.stop="viewRoomDetails(room)"
                title="查看详情"
              >
                <Eye class="action-icon" />
              </button>
              <button
                class="action-btn"
                @click.stop="editRoom(room)"
                title="编辑"
              >
                <Edit class="action-icon" />
              </button>
              <button
                class="action-btn danger"
                @click.stop="deleteRoom(room)"
                title="删除"
              >
                <Trash2 class="action-icon" />
              </button>
            </div>
          </div>

          <div class="room-content">
            <h3 class="room-name">{{ room.name }}</h3>
            <p class="room-location">{{ room.location }}</p>

            <div class="room-metrics">
              <div class="metric-item">
                <span class="metric-label">温度</span>
                <span
                  class="metric-value"
                  :class="getTemperatureClass(room.temperature)"
                >
                  {{ room.temperature }}°C
                </span>
              </div>
              <div class="metric-item">
                <span class="metric-label">湿度</span>
                <span
                  class="metric-value"
                  :class="getHumidityClass(room.humidity)"
                >
                  {{ room.humidity }}%
                </span>
              </div>
              <div class="metric-item">
                <span class="metric-label">机柜</span>
                <span class="metric-value"> {{ room.rackCount }} 个 </span>
              </div>
            </div>

            <div class="room-footer">
              <div class="utilization-bar">
                <div class="utilization-label">利用率</div>
                <div class="utilization-progress">
                  <div
                    class="utilization-fill"
                    :style="{ width: room.utilization + '%' }"
                    :class="getUtilizationClass(room.utilization)"
                  ></div>
                </div>
                <div class="utilization-text">{{ room.utilization }}%</div>
              </div>
              <div class="double-click-hint">
                <span class="hint-text">双击进入机柜布局</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 添加新机房卡片 -->
        <div
          class="room-card add-room-card"
          @click="showAddRoomDialog = true"
        >
          <div class="add-room-content">
            <Plus class="add-icon" />
            <h3 class="add-title">添加新机房</h3>
            <p class="add-description">点击创建新的机房</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 新增机房对话框 -->
    <div
      v-if="showAddRoomDialog"
      class="modal-overlay"
      @click="closeAddRoomDialog"
    >
      <div
        class="modal-content"
        @click.stop
      >
        <div class="modal-header">
          <h2 class="modal-title">新增机房</h2>
          <button
            class="modal-close"
            @click="closeAddRoomDialog"
          >
            <X class="close-icon" />
          </button>
        </div>

        <div class="modal-body">
          <form @submit.prevent="saveRoom">
            <div class="form-grid">
              <div class="form-group">
                <label class="form-label">机房名称</label>
                <input
                  v-model="roomForm.name"
                  type="text"
                  class="form-input"
                  placeholder="请输入机房名称"
                  required
                />
              </div>
              <div class="form-group">
                <label class="form-label">机房位置</label>
                <input
                  v-model="roomForm.location"
                  type="text"
                  class="form-input"
                  placeholder="请输入机房位置"
                  required
                />
              </div>
              <div class="form-group">
                <label class="form-label">机房面积</label>
                <input
                  v-model="roomForm.area"
                  type="number"
                  class="form-input"
                  placeholder="请输入机房面积(平方米)"
                  step="0.1"
                />
              </div>
              <div class="form-group">
                <label class="form-label">机柜数量</label>
                <input
                  v-model="roomForm.rackCount"
                  type="number"
                  class="form-input"
                  placeholder="请输入机柜数量"
                  min="1"
                />
              </div>
            </div>
            <div class="form-group full-width">
              <label class="form-label">备注</label>
              <textarea
                v-model="roomForm.description"
                class="form-textarea"
                placeholder="请输入备注信息"
                rows="3"
              ></textarea>
            </div>
          </form>
        </div>

        <div class="modal-footer">
          <button
            class="btn btn-secondary"
            @click="closeAddRoomDialog"
          >
            取消
          </button>
          <button
            class="btn btn-primary"
            :disabled="saving"
            @click="saveRoom"
          >
            <Loader2
              v-if="saving"
              class="btn-icon animate-spin"
            />
            {{ saving ? '保存中...' : '保存' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 机房详情弹窗 -->
    <div
      v-if="showRoomDetailModal"
      class="modal-overlay"
      @click="closeRoomDetailModal"
    >
      <div
        class="modal-content detail-modal"
        @click.stop
      >
        <div class="modal-header">
          <h2 class="modal-title">机房详情</h2>
          <button
            class="modal-close"
            @click="closeRoomDetailModal"
          >
            <X class="close-icon" />
          </button>
        </div>

        <div
          class="modal-body"
          v-if="selectedRoom"
        >
          <div class="detail-grid">
            <div class="detail-item">
              <label class="detail-label">机房名称</label>
              <div class="detail-value">{{ selectedRoom?.name }}</div>
            </div>
            <div class="detail-item">
              <label class="detail-label">机房位置</label>
              <div class="detail-value">{{ selectedRoom?.location }}</div>
            </div>
            <div class="detail-item">
              <label class="detail-label">机房面积</label>
              <div class="detail-value">{{ selectedRoom?.area }} 平方米</div>
            </div>
            <div class="detail-item">
              <label class="detail-label">机柜数量</label>
              <div class="detail-value">{{ selectedRoom?.rackCount }} 个</div>
            </div>
            <div class="detail-item">
              <label class="detail-label">运行状态</label>
              <div class="detail-value">
                <span
                  class="status-badge"
                  :class="selectedRoom?.status"
                >
                  {{ getStatusText(selectedRoom?.status) }}
                </span>
              </div>
            </div>
            <div class="detail-item">
              <label class="detail-label">当前温度</label>
              <div
                class="detail-value"
                :class="getTemperatureClass(selectedRoom?.temperature)"
              >
                {{ selectedRoom?.temperature }}°C
              </div>
            </div>
            <div class="detail-item">
              <label class="detail-label">当前湿度</label>
              <div
                class="detail-value"
                :class="getHumidityClass(selectedRoom?.humidity)"
              >
                {{ selectedRoom?.humidity }}%
              </div>
            </div>
            <div class="detail-item">
              <label class="detail-label">利用率</label>
              <div class="detail-value">
                <div class="utilization-display">
                  <div class="utilization-bar-small">
                    <div
                      class="utilization-fill-small"
                      :style="{ width: (selectedRoom?.utilization || 0) + '%' }"
                      :class="getUtilizationClass(selectedRoom.utilization)"
                    ></div>
                  </div>
                  <span class="utilization-text-small">{{ selectedRoom.utilization }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button
            class="btn btn-secondary"
            @click="closeRoomDetailModal"
          >
            关闭
          </button>
          <button
            class="btn btn-primary"
            @click="editRoom(selectedRoom)"
          >
            <Edit class="btn-icon" />
            编辑机房
          </button>
        </div>
      </div>
    </div>

    <!-- 编辑机房弹窗 -->
    <div
      v-if="showEditRoomModal"
      class="modal-overlay"
      @click="closeEditRoomModal"
    >
      <div
        class="modal-content"
        @click.stop
      >
        <div class="modal-header">
          <h2 class="modal-title">编辑机房</h2>
          <button
            class="modal-close"
            @click="closeEditRoomModal"
          >
            <X class="close-icon" />
          </button>
        </div>

        <div class="modal-body">
          <form @submit.prevent="updateRoom">
            <div class="form-grid">
              <div class="form-group">
                <label class="form-label">机房名称</label>
                <input
                  v-model="roomForm.name"
                  type="text"
                  class="form-input"
                  placeholder="请输入机房名称"
                  required
                />
              </div>
              <div class="form-group">
                <label class="form-label">机房位置</label>
                <input
                  v-model="roomForm.location"
                  type="text"
                  class="form-input"
                  placeholder="请输入机房位置"
                  required
                />
              </div>
              <div class="form-group">
                <label class="form-label">机房面积</label>
                <input
                  v-model="roomForm.area"
                  type="number"
                  class="form-input"
                  placeholder="请输入机房面积(平方米)"
                  step="0.1"
                />
              </div>
              <div class="form-group">
                <label class="form-label">机柜数量</label>
                <input
                  v-model="roomForm.rackCount"
                  type="number"
                  class="form-input"
                  placeholder="请输入机柜数量"
                  min="1"
                />
              </div>
            </div>
            <div class="form-group full-width">
              <label class="form-label">备注</label>
              <textarea
                v-model="roomForm.description"
                class="form-textarea"
                placeholder="请输入备注信息"
                rows="3"
              ></textarea>
            </div>
          </form>
        </div>

        <div class="modal-footer">
          <button
            class="btn btn-secondary"
            @click="closeEditRoomModal"
          >
            取消
          </button>
          <button
            class="btn btn-primary"
            :disabled="saving"
            @click="updateRoom"
          >
            <Loader2
              v-if="saving"
              class="btn-icon animate-spin"
            />
            {{ saving ? '保存中...' : '保存' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import eventBus from '../../utils/eventBus'
import unifiedDataService, { RoomResource } from '../../services/unifiedEnterpriseDataService'
import dataMigrationService from '../../services/dataMigrationService'
import {
  Building,
  ArrowLeft,
  RefreshCw,
  Plus,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Activity,
  Search,
  Download,
  Eye,
  Edit,
  Trash2,
  X,
  Loader2
} from 'lucide-vue-next'

const router = useRouter()

// 响应式数据
const showAddRoomDialog = ref(false)
const showRoomDetailModal = ref(false)
const showEditRoomModal = ref(false)
const saving = ref(false)
const searchKeyword = ref('')
const statusFilter = ref('all')
const selectedRoom = ref<any>(null)

// 环境监控数据 - 用于机房卡片显示
const currentEnvironment = ref({
  temperature: 22,
  humidity: 45,
  powerConsumption: 75
})

// 机房表单数据
const roomForm = ref({
  name: '',
  location: '',
  area: 0,
  rackCount: 0,
  description: ''
})

// 初始化数据迁移
const initializeData = async () => {
  // 检查是否需要数据迁移
  if (dataMigrationService.needsMigration()) {
    console.log('执行数据迁移...')
    try {
      await dataMigrationService.migrateAllData()
      console.log('数据迁移完成')
    } catch (error) {
      console.error('数据迁移失败:', error)
    }
  }
}

// 机房数据（从统一数据服务获取）
const rooms = computed(() => {
  return unifiedDataService.getRooms().map((room) => ({
    id: room.id,
    name: room.name,
    location: room.location,
    area: room.area,
    rackCount: room.rackCount,
    status: room.status,
    temperature: room.temperature || 22,
    humidity: room.humidity || 45,
    utilization: room.utilization || 0,
    description: room.description
  }))
})

// 计算属性
const totalRooms = computed(() => rooms.value.length)
const normalRooms = computed(() => rooms.value.filter((room) => room.status === 'normal').length)
const warningRooms = computed(() => rooms.value.filter((room) => room.status === 'warning').length)
const criticalRooms = computed(() => rooms.value.filter((room) => room.status === 'critical').length)
const totalRacks = computed(() => rooms.value.reduce((sum, room) => sum + room.rackCount, 0))

const filteredRooms = computed(() => {
  let filtered = rooms.value

  // 状态筛选
  if (statusFilter.value !== 'all') {
    filtered = filtered.filter((room) => room.status === statusFilter.value)
  }

  // 搜索筛选
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter((room) => room.name.toLowerCase().includes(keyword) || room.location.toLowerCase().includes(keyword))
  }

  return filtered
})

// 搜索方法
const handleSearch = () => {
  // 触发响应式更新，确保搜索结果实时显示
  // 搜索逻辑在 filteredRooms 计算属性中处理
  console.log('搜索机房:', searchKeyword.value)
}

// 方法
const goBack = () => {
  router.push('/')
  setTimeout(() => {
    eventBus.emit('openUserTab', 'enterpriseResourceManagement')
  }, 100)
}

const refreshData = () => {
  // 刷新统一数据服务中的数据
  console.log('机房数据已刷新')

  // 更新环境数据
  updateEnvironmentData()
}

const exportData = () => {
  // 导出数据逻辑
  console.log('导出机房数据')
}

const closeAddRoomDialog = () => {
  showAddRoomDialog.value = false
  resetForm()
}

const resetForm = () => {
  roomForm.value = {
    name: '',
    location: '',
    area: 0,
    rackCount: 0,
    description: ''
  }
}

const saveRoom = async () => {
  if (!roomForm.value.name || !roomForm.value.location) {
    alert('请填写必填字段')
    return
  }

  saving.value = true

  try {
    // 模拟保存
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 使用统一数据服务添加机房
    const newRoomData: Omit<RoomResource, 'id' | 'createdAt' | 'updatedAt'> = {
      name: roomForm.value.name,
      type: 'room',
      status: 'normal',
      location: roomForm.value.location,
      area: roomForm.value.area,
      rackCount: roomForm.value.rackCount,
      temperature: 22,
      humidity: 45,
      utilization: 0,
      description: roomForm.value.description
    }

    unifiedDataService.addRoom(newRoomData)
    console.log('新机房已保存到统一数据服务')

    closeAddRoomDialog()
    alert('机房创建成功！')
  } catch (error) {
    alert('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

const selectRoom = (room: any) => {
  console.log('选择机房:', room)
  // 导航到机柜布局管理页面
  navigateToRoomLayout(room)
}

const navigateToRoomLayout = (room: any) => {
  router.push({
    name: 'RoomRackLayout',
    params: { roomId: room.id },
    query: {
      roomName: room.name,
      rackCount: room.rackCount
    }
  })
}

/**
 * 查看机房详情
 * @param room 机房对象
 */
const viewRoomDetails = (room: any) => {
  selectedRoom.value = room
  showRoomDetailModal.value = true
}

/**
 * 编辑机房信息
 * @param room 机房对象
 */
const editRoom = (room: any) => {
  selectedRoom.value = room
  // 将机房数据填充到表单中
  roomForm.value = {
    name: room.name,
    location: room.location,
    area: room.area,
    rackCount: room.rackCount,
    description: room.description || ''
  }
  showEditRoomModal.value = true
}

/**
 * 关闭机房详情弹窗
 */
const closeRoomDetailModal = () => {
  showRoomDetailModal.value = false
  selectedRoom.value = null
}

/**
 * 关闭编辑机房弹窗
 */
const closeEditRoomModal = () => {
  showEditRoomModal.value = false
  selectedRoom.value = null
  resetForm()
}

/**
 * 更新机房信息
 */
const updateRoom = async () => {
  if (!roomForm.value.name || !roomForm.value.location) {
    alert('请填写必填字段')
    return
  }

  saving.value = true

  try {
    // 模拟保存
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 使用统一数据服务更新机房
    const success = unifiedDataService.updateRoom(selectedRoom.value?.id, {
      name: roomForm.value.name,
      location: roomForm.value.location,
      area: roomForm.value.area,
      rackCount: roomForm.value.rackCount,
      description: roomForm.value.description
    })

    if (success) {
      console.log('机房数据已更新到统一数据服务')
      closeEditRoomModal()
      alert('机房信息更新成功！')
    } else {
      alert('更新失败，机房不存在')
    }
  } catch (error) {
    alert('更新失败，请重试')
  } finally {
    saving.value = false
  }
}

const deleteRoom = (room: any) => {
  if (confirm(`确定要删除机房 "${room.name}" 吗？`)) {
    // 使用统一数据服务删除机房
    const success = unifiedDataService.deleteRoom(room.id)

    if (success) {
      console.log('机房已从统一数据服务中删除')
      alert('机房删除成功！')
    } else {
      alert('删除失败，机房不存在')
    }
  }
}

const getStatusText = (status: string) => {
  const statusMap = {
    normal: '正常',
    warning: '告警',
    critical: '故障'
  }
  return statusMap[status] || '未知'
}

const getTemperatureClass = (temperature: number) => {
  if (temperature > 30) return 'danger'
  if (temperature > 25) return 'warning'
  return 'normal'
}

const getHumidityClass = (humidity: number) => {
  if (humidity > 70) return 'danger'
  if (humidity > 60) return 'warning'
  return 'normal'
}

const getUtilizationClass = (utilization: number) => {
  if (utilization > 90) return 'danger'
  if (utilization > 80) return 'warning'
  return 'normal'
}

const getPowerClass = (power: number) => {
  if (power > 85) return 'danger'
  if (power > 70) return 'warning'
  return 'normal'
}

const getRoomName = (roomId: number) => {
  const room = rooms.value.find((r) => r.id === roomId)
  return room ? room.name : '未知机房'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    online: '在线',
    offline: '离线',
    warning: '警告',
    error: '错误'
  }
  return labels[status] || status
}

// 模拟环境数据更新
const updateEnvironmentData = () => {
  // 为每个机房生成随机的环境数据并更新到统一数据服务
  const currentRooms = unifiedDataService.getRooms()

  currentRooms.forEach((room) => {
    const newTemperature = Math.floor(Math.random() * 10) + 18 // 18-28°C
    const newHumidity = Math.floor(Math.random() * 30) + 35 // 35-65%

    unifiedDataService.updateRoom(room.id, {
      temperature: newTemperature,
      humidity: newHumidity
    })
  })

  console.log('环境数据已更新到统一数据服务')
}

onMounted(async () => {
  // 组件挂载后的初始化逻辑
  console.log('机房管理页面已加载')

  // 初始化数据迁移
  await initializeData()

  // 初始化环境数据
  updateEnvironmentData()

  // 更新所有机房的真实使用率
  unifiedDataService.updateAllRoomUtilizations()
  console.log('机房使用率已更新')

  // 每30秒更新一次环境数据和使用率
  setInterval(() => {
    updateEnvironmentData()
    try {
      unifiedDataService.updateAllRoomUtilizations()
    } catch (error) {
      console.error('定时更新机房使用率失败:', error)
    }
  }, 30000)
})
</script>

<style scoped>
.room-management {
  width: 100%;
  height: 100vh;
  background: #f8fafc;
  overflow-y: auto;
  -webkit-app-region: no-drag;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  color: #1e293b !important;
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  margin-bottom: 24px;
  border: 1px solid #e2e8f0;
}

.page-header * {
  color: #1e293b !important;
}

.page-title {
  color: #1e293b !important;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.page-description {
  color: #64748b !important;
  margin: 4px 0 0 0;
  font-size: 14px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.btn-back {
  background: rgba(30, 41, 59, 0.1);
  border: 2px solid rgba(30, 41, 59, 0.2);
  color: #1e293b;
  padding: 8px 16px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-back:hover {
  background: rgba(30, 41, 59, 0.15);
  border-color: rgba(30, 41, 59, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.page-title {
  font-size: 28px;
  font-weight: 900;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  z-index: 3;
}

.title-icon {
  width: 32px;
  height: 32px;
}

.page-description {
  font-size: 14px;
  opacity: 0.9;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.header-actions {
  display: flex;
  gap: 12px;
}

.btn {
  padding: 10px 16px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  transition: all 0.2s;
  font-size: 14px;
}

.btn-secondary {
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #cbd5e1;
}

.btn-secondary:hover {
  background: #e2e8f0;
  color: #334155;
  transform: translateY(-1px);
}

.btn-primary {
  background: #10b981;
  color: white;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.btn-primary:hover {
  background: #059669;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.4);
}

.btn-icon {
  width: 16px;
  height: 16px;
}

/* 统计卡片区域 */
.stats-section {
  padding: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: all 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.stat-card.success {
  border-left: 4px solid #10b981;
}

.stat-card.warning {
  border-left: 4px solid #f59e0b;
}

.stat-card.danger {
  border-left: 4px solid #ef4444;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.success .card-icon {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.warning .card-icon {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.danger .card-icon {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.card-icon .icon {
  width: 24px;
  height: 24px;
}

.card-meta {
  text-align: right;
}

.trend-indicator {
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
}

.trend-indicator.positive {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.trend-indicator.negative {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.trend-indicator.neutral {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
}

.trend-text {
  font-size: 12px;
  color: #6b7280;
  display: block;
  margin-top: 2px;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric-value {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
}

.metric-label {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.metric-description {
  font-size: 12px;
  color: #9ca3af;
}

/* 工具栏 */
.toolbar-section {
  padding: 0 24px 24px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.search-box {
  position: relative;
  min-width: 300px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  color: #6b7280;
}

.search-input {
  width: 100%;
  padding: 10px 12px 10px 40px;
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 8px;
  font-size: 14px;
  background: var(--bg-color-secondary, white);
  color: var(--text-color, #374151);
  transition: all 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: var(--input-focus-border, #3b82f6);
  box-shadow: var(--input-focus-shadow, 0 0 0 3px rgba(59, 130, 246, 0.1));
}

.search-input::placeholder {
  color: var(--text-color-tertiary, #9ca3af);
}

.filter-tabs {
  display: flex;
  gap: 8px;
}

.filter-tab {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  color: #6b7280;
}

.filter-tab:hover {
  border-color: #3b82f6;
  color: #3b82f6;
}

.filter-tab.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

/* 机房列表 */
.rooms-section {
  padding: 0 24px 24px 24px;
}

.rooms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.room-card {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  transition: all 0.2s;
  cursor: pointer;
}

.room-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.room-card.normal {
  border-left: 4px solid #10b981;
}

.room-card.warning {
  border-left: 4px solid #f59e0b;
}

.room-card.critical {
  border-left: 4px solid #ef4444;
}

.room-header {
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f3f4f6;
}

.room-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-indicator.normal {
  background: #10b981;
}

.status-indicator.warning {
  background: #f59e0b;
}

.status-indicator.critical {
  background: #ef4444;
}

.status-text {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
}

.room-actions {
  display: flex;
  gap: 4px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f3f4f6;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn:hover {
  background: #e5e7eb;
}

.action-btn.danger:hover {
  background: #fef2f2;
  color: #ef4444;
}

.action-icon {
  width: 14px;
  height: 14px;
}

.room-content {
  padding: 16px;
}

.room-name {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.room-location {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 16px 0;
}

.room-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 16px;
}

.metric-item {
  text-align: center;
}

.metric-label {
  font-size: 12px;
  color: #6b7280;
  display: block;
  margin-bottom: 4px;
}

.metric-value {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.metric-value.normal {
  color: #10b981;
}

.metric-value.warning {
  color: #f59e0b;
}

.metric-value.danger {
  color: #ef4444;
}

.room-footer {
  border-top: 1px solid #f3f4f6;
  padding-top: 12px;
}

.double-click-hint {
  margin-top: 8px;
  text-align: center;
}

.hint-text {
  font-size: 11px;
  color: #6b7280;
  background: #f9fafb;
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
  display: inline-block;
  transition: all 0.2s;
}

.room-card:hover .hint-text {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.utilization-bar {
  display: flex;
  align-items: center;
  gap: 8px;
}

.utilization-label {
  font-size: 12px;
  color: #6b7280;
  min-width: 40px;
}

.utilization-progress {
  flex: 1;
  height: 6px;
  background: #f3f4f6;
  border-radius: 3px;
  overflow: hidden;
}

.utilization-fill {
  height: 100%;
  transition: width 0.3s;
}

.utilization-fill.normal {
  background: #10b981;
}

.utilization-fill.warning {
  background: #f59e0b;
}

.utilization-fill.danger {
  background: #ef4444;
}

.utilization-text {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  min-width: 35px;
  text-align: right;
}

/* 添加机房卡片 */
.add-room-card {
  border: 2px dashed #d1d5db;
  background: #f9fafb;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.add-room-card:hover {
  border-color: #3b82f6;
  background: #eff6ff;
}

.add-room-content {
  text-align: center;
  color: #6b7280;
}

.add-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto 12px;
  color: #9ca3af;
}

.add-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 4px 0;
}

.add-description {
  font-size: 14px;
  margin: 0;
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(8px);
  -webkit-app-region: no-drag;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  z-index: 10000;
}

.modal-header {
  padding: 24px 24px 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 24px;
}

.modal-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.modal-close {
  width: 32px;
  height: 32px;
  border: none;
  background: #f3f4f6;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.modal-close:hover {
  background: #e5e7eb;
}

.close-icon {
  width: 16px;
  height: 16px;
}

.modal-body {
  padding: 0 24px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.form-input,
.form-textarea {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.2s;
  background: white;
  color: #1f2937;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.modal-footer {
  padding: 24px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 详情弹窗样式 */
.detail-modal {
  max-width: 600px;
  width: 90%;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-value {
  font-size: 14px;
  color: #1a1a1a;
  font-weight: 600;
}

.detail-value.normal {
  color: #10b981;
}

.detail-value.warning {
  color: #f59e0b;
}

.detail-value.danger {
  color: #ef4444;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.normal {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.status-badge.warning {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.status-badge.critical {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.utilization-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.utilization-bar-small {
  flex: 1;
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.utilization-fill-small {
  height: 100%;
  transition: width 0.3s ease;
}

.utilization-fill-small.normal {
  background: #10b981;
}

.utilization-fill-small.warning {
  background: #f59e0b;
}

.utilization-fill-small.danger {
  background: #ef4444;
}

.utilization-text-small {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  min-width: 35px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .toolbar-section {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .toolbar-left {
    flex-direction: column;
    gap: 12px;
  }

  .search-box {
    min-width: auto;
  }

  .filter-tabs {
    flex-wrap: wrap;
  }

  .rooms-grid {
    grid-template-columns: 1fr;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .modal-content {
    width: 95%;
    margin: 20px;
  }
}
</style>
